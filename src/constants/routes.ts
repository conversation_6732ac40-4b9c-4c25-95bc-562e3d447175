/**
 * 路由路径常量
 * 集中管理所有路由路径，避免路径不一致的问题
 */

// 主要路由路径
export const ROUTES = {
  // 首页
  HOME: "/",
  
  // 用户相关
  USER: {
    INFO: "/user/info",
    INFO_DETAIL: "/user/info/detail",
    PRODUCTION_STATS: "/user/production-stats",
    INVITE_CODE_MANAGEMENT: "/user/invite-code-management",
  },
  
  // 资源相关
  RESOURCE: {
    SOUND: "/resource/sound",
    DIGITAL_HUMAN: "/resource/digital-human",
  },
  
  // 渲染日志
  RENDER_LOG: "/render-log",
  
  // 其他页面
  CALENDAR: "/calendar",
  PROFILE: "/profile",
  
  // 表单相关
  FORM: {
    ELEMENTS: "/form-elements",
  },
  
  // 表格相关
  TABLE: {
    BASIC: "/basic-tables",
  },
  
  // 页面相关
  PAGE: {
    BLANK: "/blank",
    ERROR_404: "/error-404",
  },
  
  // UI 元素
  UI: {
    BUTTONS: "/ui-elements/buttons",
    BADGES: "/ui-elements/badges",
  },
  
  // 认证相关
  AUTH: {
    SIGNIN: "/auth/signin",
    SIGNUP: "/auth/signup",
  },
} as const;

// 导出类型，用于 TypeScript 类型检查
export type RouteKeys = typeof ROUTES;
export type RoutePaths = RouteKeys[keyof RouteKeys];

// 辅助函数：生成带参数的路由
export const generateRoute = {
  userDetail: (id: string) => `${ROUTES.USER.INFO_DETAIL}?id=${id}`,
} as const;

// 路由组配置，用于侧边栏导航
export const ROUTE_GROUPS = {
  USER: [
    ROUTES.USER.INFO,
    ROUTES.USER.INFO_DETAIL,
    ROUTES.USER.PRODUCTION_STATS,
    ROUTES.USER.INVITE_CODE_MANAGEMENT,
  ],
  RESOURCE: [
    ROUTES.RESOURCE.SOUND,
    ROUTES.RESOURCE.DIGITAL_HUMAN,
  ],
} as const;

// 面包屑配置
export const BREADCRUMB_CONFIG = {
  [ROUTES.HOME]: { title: "nav.dashboard", parent: null },
  [ROUTES.USER.INFO]: { title: "nav.userInfo", parent: null },
  [ROUTES.USER.INFO_DETAIL]: { title: "nav.userDetail", parent: ROUTES.USER.INFO },
  [ROUTES.USER.PRODUCTION_STATS]: { title: "nav.userProductionStats", parent: null },
  [ROUTES.USER.INVITE_CODE_MANAGEMENT]: { title: "nav.inviteCodeManagement", parent: null },
  [ROUTES.RESOURCE.SOUND]: { title: "nav.sound", parent: null },
  [ROUTES.RESOURCE.DIGITAL_HUMAN]: { title: "nav.digitalHuman", parent: null },
  [ROUTES.RENDER_LOG]: { title: "nav.renderLog", parent: null },
} as const;
