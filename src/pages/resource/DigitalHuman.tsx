import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import PageBreadcrumb from "@component/common/PageBreadCrumb";
import PageMeta from "@component/common/PageMeta";
import ComponentCard from "@component/common/ComponentCard";
import Label from "@component/form/Label";
import Select from "@component/ui/select/Selection";
import DatePicker from "@component/form/date-picker";
import Button from "@component/ui/button/Button";
import Badge from "@component/ui/badge/Badge";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@component/ui/table";
import { ChevronUpIcon, PlusIcon, CopyIcon, PencilIcon, TrashBinIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";
import { useModal } from "@hooks/useModal";
import CreateDigitalHumanDialog, {
  DigitalHumanFormData,
} from "@component/features/digital-human/CreateDigitalHumanDialog";

// 数字人数据类型定义
interface DigitalHumanData {
  id: number;
  name: string;
  cover: string;
  category: string;
  gender: string;
  age: string;
  memberType: string;
  accessories: string;
  emotion: string;
  skinColor: string;
  digitalHumanCategory: string;
  usageCount: number;
  favoriteCount: number;
  createDate: string;
  status: "enabled" | "disabled";
}

// 模拟数据
const mockDigitalHumanData: DigitalHumanData[] = [
  {
    id: 1,
    name: "艾莉丝",
    cover: "/images/digital-human/dh-01.jpg",
    category: "商务",
    gender: "女性",
    age: "25-30",
    memberType: "VIP",
    accessories: "眼镜",
    emotion: "友好",
    skinColor: "白皙",
    digitalHumanCategory: "虚拟主播",
    usageCount: 1250,
    favoriteCount: 89,
    createDate: "2025-07-15 10:30:00",
    status: "enabled",
  },
  {
    id: 2,
    name: "杰克",
    cover: "/images/digital-human/dh-02.jpg",
    category: "教育",
    gender: "男性",
    age: "30-35",
    memberType: "普通",
    accessories: "无",
    emotion: "专业",
    skinColor: "中等",
    digitalHumanCategory: "教学助手",
    usageCount: 890,
    favoriteCount: 67,
    createDate: "2025-07-14 14:20:00",
    status: "enabled",
  },
  {
    id: 3,
    name: "莉娜",
    cover: "/images/digital-human/dh-03.jpg",
    category: "娱乐",
    gender: "女性",
    age: "20-25",
    memberType: "VIP",
    accessories: "耳环",
    emotion: "活泼",
    skinColor: "白皙",
    digitalHumanCategory: "娱乐主持",
    usageCount: 2100,
    favoriteCount: 156,
    createDate: "2025-07-13 09:15:00",
    status: "enabled",
  },
  {
    id: 4,
    name: "马克",
    cover: "/images/digital-human/dh-04.jpg",
    category: "商务",
    gender: "男性",
    age: "35-40",
    memberType: "普通",
    accessories: "领带",
    emotion: "严肃",
    skinColor: "深色",
    digitalHumanCategory: "商务代表",
    usageCount: 567,
    favoriteCount: 34,
    createDate: "2025-07-12 16:45:00",
    status: "disabled",
  },
  {
    id: 5,
    name: "索菲亚",
    cover: "/images/digital-human/dh-05.jpg",
    category: "医疗",
    gender: "女性",
    age: "28-32",
    memberType: "VIP",
    accessories: "听诊器",
    emotion: "温和",
    skinColor: "中等",
    digitalHumanCategory: "医疗助手",
    usageCount: 1456,
    favoriteCount: 98,
    createDate: "2025-07-11 11:30:00",
    status: "enabled",
  },
];

export default function DigitalHuman() {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.digitalHuman.copied"),
    errorMessage: t("pages.digitalHuman.copyFailed"),
  });
  const {
    isOpen: isCreateDialogOpen,
    openModal: openCreateDialog,
    closeModal: closeCreateDialog,
  } = useModal();

  const [queryForm, setQueryForm] = useState({
    gender: "",
    age: "",
    order: "",
    situation: "",
    accessories: "",
    emotion: "",
    skinColor: "",
    startDate: "",
    endDate: "",
    status: "",
  });

  const [sortBy, setSortBy] = useState("popular"); // 排序方式

  // 性别选项
  const genderOptions = [
    { value: "female", label: t("pages.digitalHuman.gender.female") },
    { value: "male", label: t("pages.digitalHuman.gender.male") },
  ];

  // 年龄选项
  const ageOptions = [
    { value: "adult", label: t("pages.digitalHuman.age.adult") },
    { value: "youngAdult", label: t("pages.digitalHuman.age.youngAdult") },
    { value: "senior", label: t("pages.digitalHuman.age.senior") },
    { value: "kid", label: t("pages.digitalHuman.age.kid") },
  ];

  // 定制选项
  const orderOptions = [
    { value: "newlyAdded", label: t("pages.digitalHuman.order.newlyAdded") },
    { value: "hd", label: t("pages.digitalHuman.order.hd") },
    { value: "pro", label: t("pages.digitalHuman.order.pro") },
    { value: "freeSpeech", label: t("pages.digitalHuman.order.freeSpeech") },
  ];

  // 情况选项（部分主要选项）
  const situationOptions = [
    { value: "aiAvatar", label: t("pages.digitalHuman.situation.aiAvatar") },
    { value: "airport", label: t("pages.digitalHuman.situation.airport") },
    { value: "asmr", label: t("pages.digitalHuman.situation.asmr") },
    { value: "balcony", label: t("pages.digitalHuman.situation.balcony") },
    { value: "bathroom", label: t("pages.digitalHuman.situation.bathroom") },
    { value: "beach", label: t("pages.digitalHuman.situation.beach") },
    { value: "boat", label: t("pages.digitalHuman.situation.boat") },
    { value: "car", label: t("pages.digitalHuman.situation.car") },
    { value: "christmas", label: t("pages.digitalHuman.situation.christmas") },
    {
      value: "coffeeShop",
      label: t("pages.digitalHuman.situation.coffeeShop"),
    },
    { value: "cooking", label: t("pages.digitalHuman.situation.cooking") },
    { value: "drink", label: t("pages.digitalHuman.situation.drink") },
    { value: "family", label: t("pages.digitalHuman.situation.family") },
    {
      value: "firefighter",
      label: t("pages.digitalHuman.situation.firefighter"),
    },
    { value: "formal", label: t("pages.digitalHuman.situation.formal") },
    { value: "gaming", label: t("pages.digitalHuman.situation.gaming") },
    {
      value: "greenScreen",
      label: t("pages.digitalHuman.situation.greenScreen"),
    },
    { value: "grwm", label: t("pages.digitalHuman.situation.grwm") },
    { value: "gym", label: t("pages.digitalHuman.situation.gym") },
    { value: "hannukah", label: t("pages.digitalHuman.situation.hannukah") },
    {
      value: "historical",
      label: t("pages.digitalHuman.situation.historical"),
    },
    { value: "home", label: t("pages.digitalHuman.situation.home") },
    { value: "hook", label: t("pages.digitalHuman.situation.hook") },
    { value: "interview", label: t("pages.digitalHuman.situation.interview") },
    { value: "kitchen", label: t("pages.digitalHuman.situation.kitchen") },
    { value: "mall", label: t("pages.digitalHuman.situation.mall") },
    { value: "medical", label: t("pages.digitalHuman.situation.medical") },
    { value: "movement", label: t("pages.digitalHuman.situation.movement") },
    {
      value: "multiFrame",
      label: t("pages.digitalHuman.situation.multiFrame"),
    },
    { value: "nature", label: t("pages.digitalHuman.situation.nature") },
    {
      value: "newsAnchor",
      label: t("pages.digitalHuman.situation.newsAnchor"),
    },
    { value: "night", label: t("pages.digitalHuman.situation.night") },
    { value: "office", label: t("pages.digitalHuman.situation.office") },
    { value: "outside", label: t("pages.digitalHuman.situation.outside") },
    { value: "plane", label: t("pages.digitalHuman.situation.plane") },
    { value: "podcast", label: t("pages.digitalHuman.situation.podcast") },
    { value: "pointing", label: t("pages.digitalHuman.situation.pointing") },
    { value: "pool", label: t("pages.digitalHuman.situation.pool") },
    { value: "pregnant", label: t("pages.digitalHuman.situation.pregnant") },
    { value: "reverse", label: t("pages.digitalHuman.situation.reverse") },
    { value: "sitting", label: t("pages.digitalHuman.situation.sitting") },
    { value: "skit", label: t("pages.digitalHuman.situation.skit") },
    { value: "snow", label: t("pages.digitalHuman.situation.snow") },
    { value: "store", label: t("pages.digitalHuman.situation.store") },
    { value: "streaming", label: t("pages.digitalHuman.situation.streaming") },
    { value: "street", label: t("pages.digitalHuman.situation.street") },
    { value: "studio", label: t("pages.digitalHuman.situation.studio") },
    { value: "talk", label: t("pages.digitalHuman.situation.talk") },
    { value: "walking", label: t("pages.digitalHuman.situation.walking") },
    { value: "yoga", label: t("pages.digitalHuman.situation.yoga") },
  ];

  // 配件选项
  const accessoriesOptions = [
    { value: "bags", label: t("pages.digitalHuman.accessories.bags") },
    { value: "bathrobe", label: t("pages.digitalHuman.accessories.bathrobe") },
    { value: "book", label: t("pages.digitalHuman.accessories.book") },
    { value: "candle", label: t("pages.digitalHuman.accessories.candle") },
    { value: "cards", label: t("pages.digitalHuman.accessories.cards") },
    { value: "dishes", label: t("pages.digitalHuman.accessories.dishes") },
    { value: "drink", label: t("pages.digitalHuman.accessories.drink") },
    {
      value: "dumbbells",
      label: t("pages.digitalHuman.accessories.dumbbells"),
    },
    { value: "food", label: t("pages.digitalHuman.accessories.food") },
    { value: "fridge", label: t("pages.digitalHuman.accessories.fridge") },
    { value: "fruit", label: t("pages.digitalHuman.accessories.fruit") },
    { value: "glasses", label: t("pages.digitalHuman.accessories.glasses") },
    { value: "guitar", label: t("pages.digitalHuman.accessories.guitar") },
    { value: "hat", label: t("pages.digitalHuman.accessories.hat") },
    {
      value: "headphone",
      label: t("pages.digitalHuman.accessories.headphone"),
    },
    { value: "hijab", label: t("pages.digitalHuman.accessories.hijab") },
    { value: "jar", label: t("pages.digitalHuman.accessories.jar") },
    { value: "jewels", label: t("pages.digitalHuman.accessories.jewels") },
    { value: "knit", label: t("pages.digitalHuman.accessories.knit") },
    { value: "laptop", label: t("pages.digitalHuman.accessories.laptop") },
    { value: "mic", label: t("pages.digitalHuman.accessories.mic") },
    { value: "mirror", label: t("pages.digitalHuman.accessories.mirror") },
    { value: "mug", label: t("pages.digitalHuman.accessories.mug") },
    { value: "pet", label: t("pages.digitalHuman.accessories.pet") },
    { value: "phone", label: t("pages.digitalHuman.accessories.phone") },
    { value: "piano", label: t("pages.digitalHuman.accessories.piano") },
    { value: "plant", label: t("pages.digitalHuman.accessories.plant") },
    { value: "present", label: t("pages.digitalHuman.accessories.present") },
    { value: "scarf", label: t("pages.digitalHuman.accessories.scarf") },
    { value: "shoes", label: t("pages.digitalHuman.accessories.shoes") },
    { value: "suit", label: t("pages.digitalHuman.accessories.suit") },
    { value: "tools", label: t("pages.digitalHuman.accessories.tools") },
    { value: "trashCan", label: t("pages.digitalHuman.accessories.trashCan") },
    { value: "tree", label: t("pages.digitalHuman.accessories.tree") },
  ];

  // 情绪选项
  const emotionOptions = [
    { value: "calm", label: t("pages.digitalHuman.emotion.calm") },
    {
      value: "enthusiastic",
      label: t("pages.digitalHuman.emotion.enthusiastic"),
    },
    { value: "excited", label: t("pages.digitalHuman.emotion.excited") },
    { value: "frustrated", label: t("pages.digitalHuman.emotion.frustrated") },
    { value: "sad", label: t("pages.digitalHuman.emotion.sad") },
    { value: "serious", label: t("pages.digitalHuman.emotion.serious") },
    { value: "smiling", label: t("pages.digitalHuman.emotion.smiling") },
  ];

  // 肤色选项（暂时为空，等待具体的肤色值）
  const skinColorOptions: { value: string; label: string }[] = [
    // 这里可以根据实际的肤色值来添加选项
  ];

  // 状态选项
  const statusOptions = [
    { value: "enabled", label: t("pages.digitalHuman.status.enabled") },
    { value: "disabled", label: t("pages.digitalHuman.status.disabled") },
  ];

  // 排序选项
  const sortOptions = [
    { value: "popular", label: t("pages.digitalHuman.sort.popular") },
    { value: "newest", label: t("pages.digitalHuman.sort.newest") },
    { value: "a-z", label: t("pages.digitalHuman.sort.aToZ") },
    { value: "z-a", label: t("pages.digitalHuman.sort.zToA") },
  ];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    // todo 这里处理查询逻辑
    toast.success(t("pages.digitalHuman.queryComplete"), {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      gender: "",
      age: "",
      order: "",
      situation: "",
      accessories: "",
      emotion: "",
      skinColor: "",
      startDate: "",
      endDate: "",
      status: "",
    });

    toast(t("pages.digitalHuman.formReset"), {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleCreateNew = () => {
    openCreateDialog();
  };

  const handleSaveDigitalHuman = (data: DigitalHumanFormData) => {
    console.log("Create new digital human:", data);
    toast.success(t("pages.digitalHuman.createSuccess"), {
      duration: 2000,
      icon: "✨",
    });
    // TODO: 这里处理保存逻辑，比如调用API
  };

  const handleEdit = (id: number) => {
    console.log("Edit digital human:", id);
    toast.success(t("pages.digitalHuman.editSuccess"), {
      duration: 2000,
      icon: "✏️",
    });
  };

  const handleDelete = (id: number) => {
    console.log("Delete digital human:", id);
    toast.success(t("pages.digitalHuman.deleteSuccess"), {
      duration: 2000,
      icon: "🗑️",
    });
  };

  const handleCopyId = (id: number) => {
    copyToClipboard(id.toString());
  };

  // 处理各种下拉框变化
  const handleGenderChange = (value: string) => {
    setQueryForm({ ...queryForm, gender: value });
  };

  const handleAgeChange = (value: string) => {
    setQueryForm({ ...queryForm, age: value });
  };

  const handleOrderChange = (value: string) => {
    setQueryForm({ ...queryForm, order: value });
  };

  const handleSituationChange = (value: string) => {
    setQueryForm({ ...queryForm, situation: value });
  };

  const handleAccessoriesChange = (value: string) => {
    setQueryForm({ ...queryForm, accessories: value });
  };

  const handleEmotionChange = (value: string) => {
    setQueryForm({ ...queryForm, emotion: value });
  };

  const handleSkinColorChange = (value: string) => {
    setQueryForm({ ...queryForm, skinColor: value });
  };

  const handleStatusChange = (value: string) => {
    setQueryForm({ ...queryForm, status: value });
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
  };

  return (
    <>
      <PageMeta
        title={t("pages.digitalHuman.title")}
        description={t("pages.digitalHuman.description")}
      />
      <PageBreadcrumb pageTitle={t("pages.digitalHuman.breadcrumb")} />

      <div className="space-y-6">
        {/* 查询条件区域 */}
        <ComponentCard title="">
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            {/* 第一行：七个下拉框 */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7">
              {/* 性别 */}
              <div>
                <Label>{t("pages.digitalHuman.gender.label")}</Label>
                <Select
                  options={genderOptions}
                  placeholder={t("pages.digitalHuman.gender.placeholder")}
                  onChange={handleGenderChange}
                  value={queryForm.gender}
                  size="md"
                  clearable
                />
              </div>

              {/* 年龄 */}
              <div>
                <Label>{t("pages.digitalHuman.age.label")}</Label>
                <Select
                  options={ageOptions}
                  placeholder={t("pages.digitalHuman.age.placeholder")}
                  onChange={handleAgeChange}
                  value={queryForm.age}
                  size="md"
                  clearable
                />
              </div>

              {/* 定制 */}
              <div>
                <Label>{t("pages.digitalHuman.order.label")}</Label>
                <Select
                  options={orderOptions}
                  placeholder={t("pages.digitalHuman.order.placeholder")}
                  onChange={handleOrderChange}
                  value={queryForm.order}
                  size="md"
                  clearable
                />
              </div>

              {/* 配件 */}
              <div>
                <Label>{t("pages.digitalHuman.accessories.label")}</Label>
                <Select
                  options={accessoriesOptions}
                  placeholder={t("pages.digitalHuman.accessories.placeholder")}
                  onChange={handleAccessoriesChange}
                  value={queryForm.accessories}
                  size="md"
                  clearable
                />
              </div>

              {/* 情绪 */}
              <div>
                <Label>{t("pages.digitalHuman.emotion.label")}</Label>
                <Select
                  options={emotionOptions}
                  placeholder={t("pages.digitalHuman.emotion.placeholder")}
                  onChange={handleEmotionChange}
                  value={queryForm.emotion}
                  size="md"
                  clearable
                />
              </div>

              {/* 肤色 */}
              <div>
                <Label>{t("pages.digitalHuman.skinColor.label")}</Label>
                <Select
                  options={skinColorOptions}
                  placeholder={t("pages.digitalHuman.skinColor.placeholder")}
                  onChange={handleSkinColorChange}
                  value={queryForm.skinColor}
                  size="md"
                  clearable
                />
              </div>

              {/* 情况 */}
              <div>
                <Label>{t("pages.digitalHuman.situation.label")}</Label>
                <Select
                  options={situationOptions}
                  placeholder={t("pages.digitalHuman.situation.placeholder")}
                  onChange={handleSituationChange}
                  value={queryForm.situation}
                  size="md"
                  clearable
                />
              </div>
            </div>

            {/* 第二行：创建日期和状态 */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 创建日期 - 开始日期 */}
              <div>
                <DatePicker
                  id="start-date"
                  label={`${t("pages.digitalHuman.createDate")} (${t("common.start")})`}
                  placeholder="2025-06-17"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({
                      ...queryForm,
                      startDate: currentDateString,
                    });
                  }}
                />
              </div>

              {/* 创建日期 - 结束日期 */}
              <div>
                <DatePicker
                  id="end-date"
                  label={`${t("pages.digitalHuman.createDate")} (${t("common.end")})`}
                  placeholder="2025-07-18"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({ ...queryForm, endDate: currentDateString });
                  }}
                />
              </div>

              {/* 状态 */}
              <div>
                <Label>{t("pages.digitalHuman.status.label")}</Label>
                <Select
                  options={statusOptions}
                  placeholder={t("pages.digitalHuman.status.placeholder")}
                  onChange={handleStatusChange}
                  value={queryForm.status}
                  size="md"
                  clearable
                />
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex items-center gap-3 mt-6">
            <Button onClick={handleReset} variant="outline">
              {t("common.reset")}
            </Button>
            <Button onClick={handleQuery}>{t("pages.digitalHuman.query")}</Button>
            <Button
              onClick={() => setCollapsed(!collapsed)}
              variant="outline"
              endIcon={
                <ChevronUpIcon
                  className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                />
              }
            >
              {t("pages.digitalHuman.collapse")}
            </Button>
          </div>
        </ComponentCard>

        {/* 新增数字人和排序区域 */}
        <ComponentCard title="">
          <div className="flex items-center justify-between mb-6">
            {/* 新增数字人按钮 */}
            <Button onClick={handleCreateNew} startIcon={<PlusIcon className="size-4" />}>
              {t("pages.digitalHuman.createNew")}
            </Button>

            {/* 排序方式 */}
            <div className="flex items-center gap-3">
              <Label className="text-sm font-medium">{t("pages.digitalHuman.sortBy")}:</Label>
              <div className="w-48">
                <Select
                  options={sortOptions}
                  placeholder={t("pages.digitalHuman.sort.placeholder")}
                  onChange={handleSortChange}
                  value={sortBy}
                  size="md"
                />
              </div>
            </div>
          </div>
        </ComponentCard>

        {/* 数字人列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.name")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.cover")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.category")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.memberType")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.usageCount")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.favoriteCount")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.createDate")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.digitalHuman.table.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockDigitalHumanData.map(digitalHuman => (
                    <TableRow key={digitalHuman.id}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <span className="font-medium text-gray-800 text-theme-sm dark:text-white/90">
                          {digitalHuman.name}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start">
                        <div className="w-12 h-12 overflow-hidden rounded-lg">
                          <img
                            width={48}
                            height={48}
                            src={digitalHuman.cover}
                            alt={digitalHuman.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {digitalHuman.category}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <Badge
                          size="sm"
                          color={digitalHuman.memberType === "VIP" ? "warning" : "light"}
                        >
                          {digitalHuman.memberType}
                        </Badge>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {digitalHuman.usageCount.toLocaleString()}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {digitalHuman.favoriteCount}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {digitalHuman.createDate}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="flex items-center gap-2">
                          <button
                            className="text-brand-500 hover:underline flex items-center gap-1"
                            onClick={() => handleEdit(digitalHuman.id)}
                          >
                            <PencilIcon className="size-3" />
                            {t("pages.digitalHuman.actions.edit")}
                          </button>
                          <span className="text-gray-300">|</span>
                          <button
                            className="text-brand-500 hover:underline flex items-center gap-1"
                            onClick={() => handleCopyId(digitalHuman.id)}
                          >
                            <CopyIcon className="size-3" />
                            {t("pages.digitalHuman.actions.copy")}
                          </button>
                          <span className="text-gray-300">|</span>
                          <button
                            className="text-red-500 hover:underline flex items-center gap-1"
                            onClick={() => handleDelete(digitalHuman.id)}
                          >
                            <TrashBinIcon className="size-3" />
                            {t("pages.digitalHuman.actions.delete")}
                          </button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </ComponentCard>
      </div>

      {/* 新增数字人对话框 */}
      <CreateDigitalHumanDialog
        isOpen={isCreateDialogOpen}
        onClose={closeCreateDialog}
        onSave={handleSaveDigitalHuman}
      />
    </>
  );
}
