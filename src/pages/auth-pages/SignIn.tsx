import { useTranslation } from "react-i18next";
import PageMeta from "../../components/common/PageMeta";
import AuthLayout from "./AuthPageLayout";
import SignInForm from "../../components/auth/SignInForm";

export default function SignIn() {
  const { t } = useTranslation();

  return (
    <>
      <PageMeta
        title={`${t("auth.signIn.title")} | TailAdmin - React.js Admin Dashboard Template`}
        description={`This is React.js ${t("auth.signIn.title")} page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template`}
      />
      <AuthLayout>
        <SignInForm />
      </AuthLayout>
    </>
  );
}
