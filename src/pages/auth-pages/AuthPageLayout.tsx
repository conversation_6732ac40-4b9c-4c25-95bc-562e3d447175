import React from "react";
import ThemeTogglerTwo from "../../components/common/ThemeTogglerTwo";

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex items-center justify-center min-h-screen p-4">{children}</div>
      <div className="fixed z-50 flex gap-2 bottom-6 right-6">
        <ThemeTogglerTwo />
      </div>
    </div>
  );
}
