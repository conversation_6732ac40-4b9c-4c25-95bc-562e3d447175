import { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router";
import { useTranslation } from "react-i18next";
import { EyeCloseIcon, EyeIcon } from "@assets/icons";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import Checkbox from "../form/input/Checkbox";
import { useAuth } from "@/context/AuthContext";
import toast from "react-hot-toast";

export default function SignInForm() {
  const { t } = useTranslation();
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const [showPassword, setShowPassword] = useState(false);
  const [isChecked, setIsChecked] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !password.trim()) {
      toast.error(t("auth.signIn.validation.emailPasswordRequired"));
      return;
    }

    setIsLoading(true);

    try {
      const success = await login(email, password);
      if (success) {
        toast.success(t("auth.signIn.success.loginSuccess"));
        // 重定向到之前访问的页面或首页
        const from = location.state?.from?.pathname || "/";
        navigate(from, { replace: true });
      } else {
        toast.error(t("auth.signIn.validation.invalidCredentials"));
      }
    } catch (error) {
      toast.error(t("auth.signIn.validation.loginFailed"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-lg mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        {/* 标题区域 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t("auth.signIn.title")}
            </h1>
            <Link to="/" className="text-sm text-red-500 hover:text-red-600 dark:text-red-400">
              {t("auth.signIn.close")}
            </Link>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">{t("auth.signIn.subtitle")}</p>
        </div>

        {/* 登录表单 */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label>
              {t("auth.signIn.email")}{" "}
              <span className="text-red-500">{t("auth.signIn.required")}</span>
            </Label>
            <Input
              type="text"
              placeholder={t("auth.signIn.emailPlaceholder")}
              value={email}
              onChange={e => setEmail(e.target.value)}
              disabled={isLoading}
            />
          </div>
          <div>
            <Label>
              {t("auth.signIn.password")}{" "}
              <span className="text-red-500">{t("auth.signIn.required")}</span>
            </Label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                placeholder={t("auth.signIn.passwordPlaceholder")}
                value={password}
                onChange={e => setPassword(e.target.value)}
                disabled={isLoading}
              />
              <span
                onClick={() => setShowPassword(!showPassword)}
                className="absolute z-30 -translate-y-1/2 cursor-pointer right-4 top-1/2"
              >
                {showPassword ? (
                  <EyeIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                ) : (
                  <EyeCloseIcon className="fill-gray-500 dark:fill-gray-400 size-5" />
                )}
              </span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Checkbox checked={isChecked} onChange={setIsChecked} />
              <span className="text-sm text-gray-700 dark:text-gray-400">
                {t("auth.signIn.keepLoggedIn")}
              </span>
            </div>
            {/* <Link
              to="/reset-password"
              className="text-sm text-blue-500 hover:text-blue-600 dark:text-blue-400"
            >
              {t("auth.signIn.forgotPassword")}
            </Link> */}
          </div>

          <div>
            <button
              type="submit"
              className="w-full py-3 px-4 bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? t("auth.signIn.signingIn") : t("auth.signIn.signInButton")}
            </button>
          </div>
        </form>

        {/* 注册链接 */}
        {/* <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            还没有账户？{" "}
            <Link
              to="/signup"
              className="text-red-500 hover:text-red-600 dark:text-red-400 font-medium"
            >
              注册
            </Link>
          </p>
        </div> */}
      </div>
    </div>
  );
}
