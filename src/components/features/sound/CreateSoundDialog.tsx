import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Modal } from "@component/ui/modal";
import Button from "@component/ui/button/Button";
import Input from "@component/form/input/InputField";
import Label from "@component/form/Label";
import Select from "@component/ui/select/Selection";

interface CreateSoundDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: SoundFormData) => void;
}

export interface SoundFormData {
  voiceName: string;
  voiceId: string;
  memberType: string;
  serviceProvider: string;
  language: string;
  age: string;
  emotion: string;
  previewText: string;
}

export default function CreateSoundDialog({ isOpen, onClose, onSave }: CreateSoundDialogProps) {
  const { t } = useTranslation();

  const [formData, setFormData] = useState<SoundFormData>({
    voiceName: "",
    voiceId: "",
    memberType: "",
    serviceProvider: "",
    language: "",
    age: "",
    emotion: "",
    previewText: "",
  });

  // 会员属性选项
  const memberTypeOptions = [
    { value: "free", label: t("pages.sound.dialog.memberTypeOptions.free") },
    { value: "vip", label: t("pages.sound.dialog.memberTypeOptions.vip") },
    { value: "premium", label: t("pages.sound.dialog.memberTypeOptions.premium") },
  ];

  // 服务商选项
  const serviceProviderOptions = [
    { value: "mega", label: t("pages.sound.serviceProviders.mega") },
    { value: "azure", label: t("pages.sound.serviceProviders.azure") },
    { value: "aws", label: t("pages.sound.serviceProviders.aws") },
    { value: "google", label: t("pages.sound.serviceProviders.google") },
  ];

  // 语种选项
  const languageOptions = [
    { value: "zh-CN", label: t("pages.sound.dialog.languageOptions.zhCN") },
    { value: "en-US", label: t("pages.sound.dialog.languageOptions.enUS") },
    { value: "ja-JP", label: t("pages.sound.dialog.languageOptions.jaJP") },
    { value: "ko-KR", label: t("pages.sound.dialog.languageOptions.koKR") },
  ];

  // 年龄选项
  const ageOptions = [
    { value: "child", label: t("pages.sound.dialog.ageOptions.child") },
    { value: "young", label: t("pages.sound.dialog.ageOptions.young") },
    { value: "adult", label: t("pages.sound.dialog.ageOptions.adult") },
    { value: "senior", label: t("pages.sound.dialog.ageOptions.senior") },
  ];

  // 情感选项
  const emotionOptions = [
    { value: "neutral", label: t("pages.sound.dialog.emotionOptions.neutral") },
    { value: "happy", label: t("pages.sound.dialog.emotionOptions.happy") },
    { value: "sad", label: t("pages.sound.dialog.emotionOptions.sad") },
    { value: "angry", label: t("pages.sound.dialog.emotionOptions.angry") },
    { value: "excited", label: t("pages.sound.dialog.emotionOptions.excited") },
    { value: "calm", label: t("pages.sound.dialog.emotionOptions.calm") },
  ];

  const handleInputChange = (field: keyof SoundFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    // 基本验证
    if (!formData.voiceName.trim()) {
      return;
    }

    onSave(formData);
    handleClose();
  };

  const handleClose = () => {
    // 重置表单
    setFormData({
      voiceName: "",
      voiceId: "",
      memberType: "",
      serviceProvider: "",
      language: "",
      age: "",
      emotion: "",
      previewText: "",
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          {t("pages.sound.dialog.title")}
        </h2>

        <div className="space-y-6">
          {/* 第一行：配音名称、配音ID、会员属性 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="voiceName">{t("pages.sound.dialog.voiceName")}</Label>
              <Input
                id="voiceName"
                type="text"
                placeholder={t("pages.sound.dialog.voiceNamePlaceholder")}
                value={formData.voiceName}
                onChange={e => handleInputChange("voiceName", e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="voiceId">{t("pages.sound.dialog.voiceId")}</Label>
              <Input
                id="voiceId"
                type="text"
                placeholder={t("pages.sound.dialog.voiceIdPlaceholder")}
                value={formData.voiceId}
                onChange={e => handleInputChange("voiceId", e.target.value)}
              />
            </div>
            <div>
              <Label>{t("pages.sound.dialog.memberType")}</Label>
              <Select
                options={memberTypeOptions}
                placeholder={t("pages.sound.dialog.memberTypePlaceholder")}
                onChange={value => handleInputChange("memberType", value)}
                value={formData.memberType}
                size="md"
                clearable
              />
            </div>
          </div>

          {/* 第二行：服务商、语种、年龄、情感 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>{t("pages.sound.dialog.serviceProvider")}</Label>
              <Select
                options={serviceProviderOptions}
                placeholder={t("pages.sound.dialog.serviceProviderPlaceholder")}
                onChange={value => handleInputChange("serviceProvider", value)}
                value={formData.serviceProvider}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.sound.dialog.language")}</Label>
              <Select
                options={languageOptions}
                placeholder={t("pages.sound.dialog.languagePlaceholder")}
                onChange={value => handleInputChange("language", value)}
                value={formData.language}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.sound.dialog.age")}</Label>
              <Select
                options={ageOptions}
                placeholder={t("pages.sound.dialog.agePlaceholder")}
                onChange={value => handleInputChange("age", value)}
                value={formData.age}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.sound.dialog.emotion")}</Label>
              <Select
                options={emotionOptions}
                placeholder={t("pages.sound.dialog.emotionPlaceholder")}
                onChange={value => handleInputChange("emotion", value)}
                value={formData.emotion}
                size="md"
                clearable
              />
            </div>
          </div>

          {/* 第三行：试听文案 */}
          <div>
            <Label htmlFor="previewText">{t("pages.sound.dialog.previewText")}</Label>
            <textarea
              id="previewText"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-transparent dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              placeholder={t("pages.sound.dialog.previewTextPlaceholder")}
              value={formData.previewText}
              onChange={e => handleInputChange("previewText", e.target.value)}
            />
          </div>
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-end gap-3 mt-8">
          <Button variant="outline" onClick={handleClose}>
            {t("pages.sound.dialog.cancel")}
          </Button>
          <Button onClick={handleSave}>{t("pages.sound.dialog.save")}</Button>
        </div>
      </div>
    </Modal>
  );
}
