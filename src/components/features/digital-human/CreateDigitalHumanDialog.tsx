import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Modal } from "@component/ui/modal";
import Button from "@component/ui/button/Button";
import Input from "@component/form/input/InputField";
import Label from "@component/form/Label";
import Select from "@component/ui/select/Selection";
import { useDropzone } from "react-dropzone";

interface CreateDigitalHumanDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: DigitalHumanFormData) => void;
}

export interface DigitalHumanFormData {
  name: string;
  digitalHumanId: string;
  defaultConfig: string;
  memberType: string;
  category: string;
  age: string;
  gender: string;
  situation: string;
  accessories: string;
  emotion: string;
  skinColor: string;
  portraitFile: File | null;
  materialFile: File | null;
}

export default function CreateDigitalHumanDialog({
  isOpen,
  onClose,
  onSave,
}: CreateDigitalHumanDialogProps) {
  const { t } = useTranslation();

  const [formData, setFormData] = useState<DigitalHumanFormData>({
    name: "",
    digitalHumanId: "",
    defaultConfig: "",
    memberType: "",
    category: "",
    age: "",
    gender: "",
    situation: "",
    accessories: "",
    emotion: "",
    skinColor: "",
    portraitFile: null,
    materialFile: null,
  });

  // 默认配置选项
  const defaultConfigOptions = [
    { value: "basic", label: t("pages.digitalHuman.dialog.defaultConfig.basic") },
    { value: "advanced", label: t("pages.digitalHuman.dialog.defaultConfig.advanced") },
    { value: "premium", label: t("pages.digitalHuman.dialog.defaultConfig.premium") },
  ];

  // 会员属性选项
  const memberTypeOptions = [
    { value: "free", label: t("pages.digitalHuman.dialog.memberType.free") },
    { value: "vip", label: t("pages.digitalHuman.dialog.memberType.vip") },
    { value: "premium", label: t("pages.digitalHuman.dialog.memberType.premium") },
  ];

  // 数字人分类选项
  const categoryOptions = [
    { value: "business", label: t("pages.digitalHuman.dialog.category.business") },
    { value: "education", label: t("pages.digitalHuman.dialog.category.education") },
    { value: "entertainment", label: t("pages.digitalHuman.dialog.category.entertainment") },
    { value: "medical", label: t("pages.digitalHuman.dialog.category.medical") },
  ];

  // 年龄选项
  const ageOptions = [
    { value: "adult", label: t("pages.digitalHuman.age.adult") },
    { value: "youngAdult", label: t("pages.digitalHuman.age.youngAdult") },
    { value: "senior", label: t("pages.digitalHuman.age.senior") },
    { value: "kid", label: t("pages.digitalHuman.age.kid") },
  ];

  // 性别选项
  const genderOptions = [
    { value: "female", label: t("pages.digitalHuman.gender.female") },
    { value: "male", label: t("pages.digitalHuman.gender.male") },
  ];

  // 情况选项（部分主要选项）
  const situationOptions = [
    { value: "aiAvatar", label: t("pages.digitalHuman.situation.aiAvatar") },
    { value: "airport", label: t("pages.digitalHuman.situation.airport") },
    { value: "asmr", label: t("pages.digitalHuman.situation.asmr") },
    { value: "balcony", label: t("pages.digitalHuman.situation.balcony") },
    { value: "bathroom", label: t("pages.digitalHuman.situation.bathroom") },
    { value: "beach", label: t("pages.digitalHuman.situation.beach") },
    { value: "boat", label: t("pages.digitalHuman.situation.boat") },
    { value: "car", label: t("pages.digitalHuman.situation.car") },
    { value: "christmas", label: t("pages.digitalHuman.situation.christmas") },
    {
      value: "coffeeShop",
      label: t("pages.digitalHuman.situation.coffeeShop"),
    },
    { value: "cooking", label: t("pages.digitalHuman.situation.cooking") },
    { value: "drink", label: t("pages.digitalHuman.situation.drink") },
    { value: "family", label: t("pages.digitalHuman.situation.family") },
    {
      value: "firefighter",
      label: t("pages.digitalHuman.situation.firefighter"),
    },
    { value: "formal", label: t("pages.digitalHuman.situation.formal") },
    { value: "gaming", label: t("pages.digitalHuman.situation.gaming") },
    {
      value: "greenScreen",
      label: t("pages.digitalHuman.situation.greenScreen"),
    },
    { value: "grwm", label: t("pages.digitalHuman.situation.grwm") },
    { value: "gym", label: t("pages.digitalHuman.situation.gym") },
    { value: "hannukah", label: t("pages.digitalHuman.situation.hannukah") },
    {
      value: "historical",
      label: t("pages.digitalHuman.situation.historical"),
    },
    { value: "home", label: t("pages.digitalHuman.situation.home") },
    { value: "hook", label: t("pages.digitalHuman.situation.hook") },
    { value: "interview", label: t("pages.digitalHuman.situation.interview") },
    { value: "kitchen", label: t("pages.digitalHuman.situation.kitchen") },
    { value: "mall", label: t("pages.digitalHuman.situation.mall") },
    { value: "medical", label: t("pages.digitalHuman.situation.medical") },
    { value: "movement", label: t("pages.digitalHuman.situation.movement") },
    {
      value: "multiFrame",
      label: t("pages.digitalHuman.situation.multiFrame"),
    },
    { value: "nature", label: t("pages.digitalHuman.situation.nature") },
    {
      value: "newsAnchor",
      label: t("pages.digitalHuman.situation.newsAnchor"),
    },
    { value: "night", label: t("pages.digitalHuman.situation.night") },
    { value: "office", label: t("pages.digitalHuman.situation.office") },
    { value: "outside", label: t("pages.digitalHuman.situation.outside") },
    { value: "plane", label: t("pages.digitalHuman.situation.plane") },
    { value: "podcast", label: t("pages.digitalHuman.situation.podcast") },
    { value: "pointing", label: t("pages.digitalHuman.situation.pointing") },
    { value: "pool", label: t("pages.digitalHuman.situation.pool") },
    { value: "pregnant", label: t("pages.digitalHuman.situation.pregnant") },
    { value: "reverse", label: t("pages.digitalHuman.situation.reverse") },
    { value: "sitting", label: t("pages.digitalHuman.situation.sitting") },
    { value: "skit", label: t("pages.digitalHuman.situation.skit") },
    { value: "snow", label: t("pages.digitalHuman.situation.snow") },
    { value: "store", label: t("pages.digitalHuman.situation.store") },
    { value: "streaming", label: t("pages.digitalHuman.situation.streaming") },
    { value: "street", label: t("pages.digitalHuman.situation.street") },
    { value: "studio", label: t("pages.digitalHuman.situation.studio") },
    { value: "talk", label: t("pages.digitalHuman.situation.talk") },
    { value: "walking", label: t("pages.digitalHuman.situation.walking") },
    { value: "yoga", label: t("pages.digitalHuman.situation.yoga") },
  ];

  // 配件选项
  const accessoriesOptions = [
    { value: "bags", label: t("pages.digitalHuman.accessories.bags") },
    { value: "bathrobe", label: t("pages.digitalHuman.accessories.bathrobe") },
    { value: "book", label: t("pages.digitalHuman.accessories.book") },
    { value: "candle", label: t("pages.digitalHuman.accessories.candle") },
    { value: "cards", label: t("pages.digitalHuman.accessories.cards") },
    { value: "dishes", label: t("pages.digitalHuman.accessories.dishes") },
    { value: "drink", label: t("pages.digitalHuman.accessories.drink") },
    {
      value: "dumbbells",
      label: t("pages.digitalHuman.accessories.dumbbells"),
    },
    { value: "food", label: t("pages.digitalHuman.accessories.food") },
    { value: "fridge", label: t("pages.digitalHuman.accessories.fridge") },
    { value: "fruit", label: t("pages.digitalHuman.accessories.fruit") },
    { value: "glasses", label: t("pages.digitalHuman.accessories.glasses") },
    { value: "guitar", label: t("pages.digitalHuman.accessories.guitar") },
    { value: "hat", label: t("pages.digitalHuman.accessories.hat") },
    {
      value: "headphone",
      label: t("pages.digitalHuman.accessories.headphone"),
    },
    { value: "hijab", label: t("pages.digitalHuman.accessories.hijab") },
    { value: "jar", label: t("pages.digitalHuman.accessories.jar") },
    { value: "jewels", label: t("pages.digitalHuman.accessories.jewels") },
    { value: "knit", label: t("pages.digitalHuman.accessories.knit") },
    { value: "laptop", label: t("pages.digitalHuman.accessories.laptop") },
    { value: "mic", label: t("pages.digitalHuman.accessories.mic") },
    { value: "mirror", label: t("pages.digitalHuman.accessories.mirror") },
    { value: "mug", label: t("pages.digitalHuman.accessories.mug") },
    { value: "pet", label: t("pages.digitalHuman.accessories.pet") },
    { value: "phone", label: t("pages.digitalHuman.accessories.phone") },
    { value: "piano", label: t("pages.digitalHuman.accessories.piano") },
    { value: "plant", label: t("pages.digitalHuman.accessories.plant") },
    { value: "present", label: t("pages.digitalHuman.accessories.present") },
    { value: "scarf", label: t("pages.digitalHuman.accessories.scarf") },
    { value: "shoes", label: t("pages.digitalHuman.accessories.shoes") },
    { value: "suit", label: t("pages.digitalHuman.accessories.suit") },
    { value: "tools", label: t("pages.digitalHuman.accessories.tools") },
    { value: "trashCan", label: t("pages.digitalHuman.accessories.trashCan") },
    { value: "tree", label: t("pages.digitalHuman.accessories.tree") },
  ];

  // 情绪选项
  const emotionOptions = [
    { value: "calm", label: t("pages.digitalHuman.emotion.calm") },
    {
      value: "enthusiastic",
      label: t("pages.digitalHuman.emotion.enthusiastic"),
    },
    { value: "excited", label: t("pages.digitalHuman.emotion.excited") },
    { value: "frustrated", label: t("pages.digitalHuman.emotion.frustrated") },
    { value: "sad", label: t("pages.digitalHuman.emotion.sad") },
    { value: "serious", label: t("pages.digitalHuman.emotion.serious") },
    { value: "smiling", label: t("pages.digitalHuman.emotion.smiling") },
  ];

  // 肤色选项（暂时为空，等待具体的肤色值）
  const skinColorOptions: { value: string; label: string }[] = [
    // 这里可以根据实际的肤色值来添加选项
  ];

  // 肖像文件上传
  const {
    getRootProps: getPortraitRootProps,
    getInputProps: getPortraitInputProps,
    isDragActive: isPortraitDragActive,
  } = useDropzone({
    onDrop: (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setFormData({ ...formData, portraitFile: acceptedFiles[0] });
      }
    },
    accept: {
      "image/jpeg": [],
      "image/png": [],
      "image/gif": [],
      "image/webp": [],
    },
    multiple: false,
  });

  // 素材文件上传
  const {
    getRootProps: getMaterialRootProps,
    getInputProps: getMaterialInputProps,
    isDragActive: isMaterialDragActive,
  } = useDropzone({
    onDrop: (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        setFormData({ ...formData, materialFile: acceptedFiles[0] });
      }
    },
    accept: {
      "video/mp4": [],
    },
    multiple: false,
  });

  const handleInputChange = (field: keyof DigitalHumanFormData, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSave = () => {
    onSave(formData);
    onClose();
    // 重置表单
    setFormData({
      name: "",
      digitalHumanId: "",
      defaultConfig: "",
      memberType: "",
      category: "",
      age: "",
      gender: "",
      situation: "",
      accessories: "",
      emotion: "",
      skinColor: "",
      portraitFile: null,
      materialFile: null,
    });
  };

  const handleCancel = () => {
    onClose();
    // 重置表单
    setFormData({
      name: "",
      digitalHumanId: "",
      defaultConfig: "",
      memberType: "",
      category: "",
      age: "",
      gender: "",
      situation: "",
      accessories: "",
      emotion: "",
      skinColor: "",
      portraitFile: null,
      materialFile: null,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-4xl mx-4">
      <div className="p-6 sm:p-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white/90 mb-6">
          {t("pages.digitalHuman.dialog.title")}
        </h2>

        <div className="space-y-6">
          {/* 第一行：基本信息 */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div>
              <Label>{t("pages.digitalHuman.dialog.name")}</Label>
              <Input
                placeholder={t("pages.digitalHuman.dialog.namePlaceholder")}
                value={formData.name}
                onChange={e => handleInputChange("name", e.target.value)}
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.dialog.digitalHumanId")}</Label>
              <Input
                placeholder={t("pages.digitalHuman.dialog.digitalHumanIdPlaceholder")}
                value={formData.digitalHumanId}
                onChange={e => handleInputChange("digitalHumanId", e.target.value)}
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.dialog.defaultConfig")}</Label>
              <Select
                options={defaultConfigOptions}
                placeholder={t("pages.digitalHuman.dialog.defaultConfigPlaceholder")}
                onChange={value => handleInputChange("defaultConfig", value)}
                value={formData.defaultConfig}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.dialog.memberType")}</Label>
              <Select
                options={memberTypeOptions}
                placeholder={t("pages.digitalHuman.dialog.memberTypePlaceholder")}
                onChange={value => handleInputChange("memberType", value)}
                value={formData.memberType}
                size="md"
                clearable
              />
            </div>
          </div>

          {/* 第二行：数字人分类 */}
          <div>
            <Label>{t("pages.digitalHuman.dialog.category")}</Label>
            <div className="max-w-xs">
              <Select
                options={categoryOptions}
                placeholder={t("pages.digitalHuman.dialog.categoryPlaceholder")}
                onChange={value => handleInputChange("category", value)}
                value={formData.category}
                size="md"
                clearable
              />
            </div>
          </div>

          {/* 第三行：属性选择器 */}
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
            <div>
              <Label>{t("pages.digitalHuman.age.label")}</Label>
              <Select
                options={ageOptions}
                placeholder={t("pages.digitalHuman.age.placeholder")}
                onChange={value => handleInputChange("age", value)}
                value={formData.age}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.gender.label")}</Label>
              <Select
                options={genderOptions}
                placeholder={t("pages.digitalHuman.gender.placeholder")}
                onChange={value => handleInputChange("gender", value)}
                value={formData.gender}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.situation.label")}</Label>
              <Select
                options={situationOptions}
                placeholder={t("pages.digitalHuman.situation.placeholder")}
                onChange={value => handleInputChange("situation", value)}
                value={formData.situation}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.accessories.label")}</Label>
              <Select
                options={accessoriesOptions}
                placeholder={t("pages.digitalHuman.accessories.placeholder")}
                onChange={value => handleInputChange("accessories", value)}
                value={formData.accessories}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.emotion.label")}</Label>
              <Select
                options={emotionOptions}
                placeholder={t("pages.digitalHuman.emotion.placeholder")}
                onChange={value => handleInputChange("emotion", value)}
                value={formData.emotion}
                size="md"
                clearable
              />
            </div>
            <div>
              <Label>{t("pages.digitalHuman.skinColor.label")}</Label>
              <Select
                options={skinColorOptions}
                placeholder={t("pages.digitalHuman.skinColor.placeholder")}
                onChange={value => handleInputChange("skinColor", value)}
                value={formData.skinColor}
                size="md"
                clearable
              />
            </div>
          </div>

          {/* 第四行：文件上传区域 */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* 肖像上传 */}
            <div>
              <Label>{t("pages.digitalHuman.dialog.portrait")}</Label>
              <div
                {...getPortraitRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isPortraitDragActive
                    ? "border-brand-500 bg-brand-50 dark:bg-brand-900/20"
                    : "border-gray-300 dark:border-gray-700 hover:border-brand-400"
                }`}
              >
                <input {...getPortraitInputProps()} />
                <div className="flex flex-col items-center">
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                    <svg
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    {formData.portraitFile
                      ? formData.portraitFile.name
                      : t("pages.digitalHuman.dialog.portraitUploadText")}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {t("pages.digitalHuman.dialog.portraitSupportText")}
                  </p>
                </div>
              </div>
            </div>

            {/* 素材上传 */}
            <div>
              <Label>{t("pages.digitalHuman.dialog.material")}</Label>
              <div
                {...getMaterialRootProps()}
                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
                  isMaterialDragActive
                    ? "border-brand-500 bg-brand-50 dark:bg-brand-900/20"
                    : "border-gray-300 dark:border-gray-700 hover:border-brand-400"
                }`}
              >
                <input {...getMaterialInputProps()} />
                <div className="flex flex-col items-center">
                  <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800">
                    <svg
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    {formData.materialFile
                      ? formData.materialFile.name
                      : t("pages.digitalHuman.dialog.materialUploadText")}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    {t("pages.digitalHuman.dialog.materialSupportText")}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={handleCancel}>
              {t("pages.digitalHuman.dialog.cancel")}
            </Button>
            <Button onClick={handleSave}>{t("pages.digitalHuman.dialog.save")}</Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
