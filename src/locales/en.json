{"nav": {"dashboard": "Dashboard", "ecommerce": "Ecommerce", "calendar": "Calendar", "userProfile": "User Profile", "users": "Users", "userInfo": "User Information", "userProductionStats": "User Production Stats", "inviteCodeManagement": "Invite Code Management", "forms": "Forms", "formElements": "Form Elements", "tables": "Tables", "basicTables": "Basic Tables", "pages": "Pages", "blankPage": "<PERSON><PERSON> <PERSON>", "404Error": "404 Error", "charts": "Charts", "lineChart": "Line Chart", "barChart": "Bar Chart", "uiElements": "UI Elements", "alerts": "<PERSON><PERSON><PERSON>", "avatar": "Avatar", "badge": "Badge", "buttons": "Buttons", "images": "Images", "videos": "Videos", "resources": "Resources", "sound": "Sound Library", "digitalHuman": "Digital Human Management", "renderLog": "Render Log", "authentication": "Authentication", "signIn": "Sign In", "signUp": "Sign Up"}, "header": {"search": "Search or type command...", "notifications": "Notifications", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "auth": {"layout": {"description": "Free and Open-Source Tailwind CSS Admin Dashboard Template"}, "signIn": {"title": "Sign In", "subtitle": "Enter your email and password to sign in!", "backToDashboard": "Back to dashboard", "signInWithGoogle": "Sign in with Google", "signInWithX": "Sign in with <PERSON>", "or": "Or", "email": "Email", "password": "Password", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "Enter your password", "keepLoggedIn": "Keep me logged in", "forgotPassword": "Forgot password?", "signInButton": "Sign in", "noAccount": "Don't have an account?", "signUpLink": "Sign Up", "required": "*"}, "signUp": {"title": "Sign Up", "subtitle": "Enter your email and password to sign up!", "backToDashboard": "Back to dashboard", "signUpWithGoogle": "Sign up with Google", "signUpWithX": "Sign up with <PERSON>", "or": "Or", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "password": "Password", "firstNamePlaceholder": "Enter your first name", "lastNamePlaceholder": "Enter your last name", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "termsAgreement": "By creating an account means you agree to the", "termsAndConditions": "Terms and Conditions,", "and": "and our", "privacyPolicy": "Privacy Policy", "signUpButton": "Sign Up", "haveAccount": "Already have an account?", "signInLink": "Sign In", "required": "*"}}, "pages": {"dashboard": {"title": "Dashboard", "ecommerce": "Ecommerce Dashboard", "description": "This is React.js Ecommerce Dashboard page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "profile": {"title": "Profile", "description": "This is React.js Profile Dashboard page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "Profile"}, "calendar": {"title": "Calendar", "description": "This is React.js Calendar page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "forms": {"title": "Form Elements", "description": "This is React.js Form Elements page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "tables": {"title": "Basic Tables", "description": "This is React.js Basic Tables page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "charts": {"lineChart": {"title": "Line Chart", "description": "This is React.js Line Chart page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "barChart": {"title": "Bar Chart", "description": "This is React.js Bar Chart page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}}, "uiElements": {"alerts": {"title": "<PERSON><PERSON><PERSON>", "description": "This is React.js Alerts page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "avatars": {"title": "Avatars", "description": "This is React.js Avatars page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "badges": {"title": "Badges", "description": "This is React.js Badges page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "buttons": {"title": "Buttons", "description": "This is React.js Buttons page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "images": {"title": "Images", "description": "This is React.js Images page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}, "videos": {"title": "Videos", "description": "This is React.js Videos page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"}}, "notFound": {"title": "404 - Page Not Found", "heading": "Oops! Page not found", "description": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "backHome": "Back to Home"}, "blank": {"title": "<PERSON><PERSON> <PERSON>", "description": "This is React.js Blank page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "content": "This is a blank page. You can add your content here."}, "userInfo": {"title": "User Information", "description": "This is React.js User Information page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "User Information", "conditionQuery": "Condition Query", "conditionField": {"select": "Please Select", "username": "Username", "userId": "User ID", "userEmail": "User Email", "inviteCode": "Invite Code"}, "conditionValue": {"placeholder": "Please enter keyword"}, "registerTime": "Register Time", "region": "Region", "registerSource": "Register Source", "query": "Query", "collapse": "Collapse", "username": "Username", "userId": "User ID", "userEmail": "User Email", "status": "Status", "actions": "Actions", "details": "Details", "modify": "Modify", "active": "Active", "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed", "regions": {"all": "All", "china": "China", "singapore": "Singapore", "usa": "USA", "uk": "UK", "canada": "Canada", "hongkong": "Hong Kong"}, "sources": {"all": "All", "inviteCode": "Invite Code"}, "userActions": {"blockUser": "Block User", "deleteUser": "Delete User", "unblockUser": "Unblock User", "more": "More"}}, "userInfoDetail": {"title": "User Details", "description": "This is React.js User Details page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "User Details", "basicInfo": "Basic Information", "storageInfo": "Storage Information", "teamInfo": "Team Information", "userId": "User ID", "userEmail": "User Email", "phone": "Phone", "region": "Region", "registerSource": "Register Source", "registerTime": "Register Time", "verificationStatus": "Verification Status", "unverified": "Unverified", "userType": "User Type", "freeUser": "Free User", "validity": "Validity", "personalSpaceUsed": "Personal Space Used", "personalSpaceLimit": "Personal Space Limit", "teamStatus": "Team Status", "teamExpiry": "Team Expiry", "teamId": "Team ID", "teamCapacity": "Team Capacity", "status": {"active": "Active", "inactive": "Inactive"}, "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed", "invalidUserId": "Invalid User ID", "userNotFound": "User Not Found"}, "userProductionStats": {"title": "User Production Stats", "description": "This is React.js User Production Stats page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "User Production Stats", "queryConditions": "Query Conditions", "conditionQuery": "Condition Query", "conditionField": {"select": "Please Select", "username": "Username", "userId": "User ID", "userEmail": "User Email"}, "conditionValue": {"placeholder": "Please enter keyword"}, "query": "Query", "collapse": "Collapse", "tabs": {"all": "All", "smartAdult": "Smart Adult", "pictureExplain": "Picture Explain", "linkShopExplain": "Link Shop Explain", "smartPhotoExplain": "Smart Photo Explain", "digitalAvatar": "Digital Avatar"}, "username": "Username", "userId": "User ID", "userEmail": "User Email", "yesterdayProduction": "Yesterday Production", "totalProduction": "Total Production", "actions": "Actions", "details": "Details", "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed", "queryComplete": "Query completed!", "formReset": "Form has been reset", "navigatingToDetails": "Navigating to user details..."}, "sound": {"title": "Sound Library", "description": "This is React.js Sound Library page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "Sound Library", "conditionQuery": "Condition Query", "conditionField": {"select": "Please Select", "voiceId": "Voice ID", "voiceName": "Voice Name", "userEmail": "User Email"}, "conditionValue": {"placeholder": "Please enter keyword"}, "createDate": "Create Date", "serviceProvider": "Service Provider", "sortBy": "Sort By", "sortOptions": {"mostPopular": "Most Popular", "newest": "Newest", "aToZ": "A-Z", "zToA": "Z-A"}, "query": "Query", "collapse": "Collapse", "voiceName": "Voice Name", "preview": "Preview", "usageCount": "Usage Count", "favoriteCount": "Favorite Count", "actions": "Actions", "play": "Play", "edit": "Edit", "copyId": "Copy ID", "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed", "createNew": "Add New Voice", "createSuccess": "Voice created successfully", "serviceProviders": {"all": "All", "mega": "MEGA", "azure": "Azure", "aws": "AWS", "google": "Google"}, "dialog": {"title": "Add New Voice", "voiceName": "Voice Name", "voiceNamePlaceholder": "Enter voice name", "voiceId": "Voice ID", "voiceIdPlaceholder": "Enter voice ID", "memberType": "Member Type", "memberTypePlaceholder": "Select member type", "serviceProvider": "Service Provider", "serviceProviderPlaceholder": "Select service provider", "language": "Language", "languagePlaceholder": "Select language", "age": "Age", "agePlaceholder": "Select age", "emotion": "Emotion", "emotionPlaceholder": "Select emotion", "previewText": "Preview Text", "previewTextPlaceholder": "Welcome to <PERSON><PERSON>, start building your digital human here to reduce your work costs", "save": "Save", "cancel": "Cancel", "memberTypeOptions": {"free": "Free User", "vip": "VIP User", "premium": "Premium User"}, "languageOptions": {"zhCN": "Chinese (Simplified)", "enUS": "English (US)", "jaJP": "Japanese", "koKR": "Korean"}, "ageOptions": {"child": "Child", "young": "<PERSON>", "adult": "Adult", "senior": "Senior"}, "emotionOptions": {"neutral": "Neutral", "happy": "Happy", "sad": "Sad", "angry": "Angry", "excited": "Excited", "calm": "Calm"}}}, "digitalHuman": {"title": "Digital Human Management", "description": "This is React.js Digital Human Management page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "Digital Human Management", "query": "Query", "collapse": "Collapse", "queryComplete": "Query completed!", "formReset": "Form has been reset", "createNew": "Create New Digital Human", "createSuccess": "Digital human created successfully!", "editSuccess": "Digital human edited successfully!", "deleteSuccess": "Digital human deleted successfully!", "copied": "<PERSON>pied", "copyFailed": "Co<PERSON> failed", "sortBy": "Sort By", "createDate": "Create Date", "dialog": {"title": "Create New Digital Human", "name": "Digital Human Name", "namePlaceholder": "Enter digital human name", "digitalHumanId": "Digital Human ID", "digitalHumanIdPlaceholder": "Enter digital human ID", "defaultConfig": "Default Configuration", "defaultConfigPlaceholder": "Select default configuration", "memberType": "Member Type", "memberTypePlaceholder": "Select member type", "category": "Digital Human Category", "categoryPlaceholder": "Select digital human category", "defaultConfigOptions": {"basic": "Basic Configuration", "advanced": "Advanced Configuration", "premium": "Premium Configuration"}, "memberTypeOptions": {"free": "Free User", "vip": "VIP User", "premium": "Premium User"}, "categoryOptions": {"business": "Business", "education": "Education", "entertainment": "Entertainment", "medical": "Medical"}, "portrait": "Portrait", "portraitUploadText": "Click or drag files here to upload", "portraitSupportText": "Supported formats: jpg, png, gif, webp", "material": "Material", "materialUploadText": "Click or drag files here to upload", "materialSupportText": "Supported formats: mp4", "save": "Save", "cancel": "Cancel"}, "gender": {"label": "Gender", "placeholder": "Select gender", "female": "Female", "male": "Male"}, "age": {"label": "Age", "placeholder": "Select age", "adult": "Adult", "youngAdult": "Young Adult", "senior": "Senior", "kid": "<PERSON>"}, "order": {"label": "Order", "placeholder": "Select order", "newlyAdded": "Newly added", "hd": "HD", "pro": "Exclusive for Pro", "freeSpeech": "Free speech"}, "situation": {"label": "Situation", "placeholder": "Select situation", "aiAvatar": "AI Avatar", "airport": "Airport", "asmr": "ASMR", "balcony": "Balcony", "bathroom": "Bathroom", "beach": "Beach", "boat": "Boat", "car": "Car", "christmas": "Christmas 🎄", "coffeeShop": "Coffee Shop", "cooking": "Cooking", "drink": "Drink", "family": "Family", "firefighter": "Firefighter", "formal": "Formal", "gaming": "Gaming", "greenScreen": "Green Screen", "grwm": "GRWM", "gym": "Gym", "hannukah": "<PERSON><PERSON><PERSON><PERSON>", "historical": "Historical", "home": "Home", "hook": "Hook", "interview": "Interview", "kitchen": "Kitchen", "mall": "Mall", "medical": "Medical", "movement": "Movement", "multiFrame": "Multi-frame", "nature": "Nature", "newsAnchor": "News Anchor", "night": "Night", "office": "Office", "outside": "Outside", "plane": "Plane", "podcast": "Podcast", "pointing": "Pointing", "pool": "Pool", "pregnant": "Pregnant", "reverse": "Reverse", "sitting": "Sitting", "skit": "<PERSON><PERSON>", "snow": "Snow", "store": "Store", "streaming": "Streaming", "street": "Street", "studio": "Studio", "talk": "Talk", "walking": "Walking", "yoga": "Yoga"}, "accessories": {"label": "Accessories", "placeholder": "Select accessories", "bags": "Bags", "bathrobe": "Bathrobe", "book": "Book", "candle": "Candle", "cards": "Cards", "dishes": "Dishes", "drink": "Drink", "dumbbells": "Dumb<PERSON>s", "food": "Food", "fridge": "<PERSON><PERSON>", "fruit": "Fruit", "glasses": "Glasses", "guitar": "Guitar", "hat": "Hat", "headphone": "Headphone", "hijab": "Hijab", "jar": "<PERSON><PERSON>", "jewels": "Jewels", "knit": "K<PERSON><PERSON>", "laptop": "Laptop", "mic": "Mic", "mirror": "Mirror", "mug": "<PERSON>g", "pet": "Pet", "phone": "Phone", "piano": "Piano", "plant": "Plant", "present": "Present", "scarf": "<PERSON><PERSON><PERSON>", "shoes": "Shoes", "suit": "Suit", "tools": "Tools", "trashCan": "Trash Can", "tree": "Tree"}, "emotion": {"label": "Emotions", "placeholder": "Select emotion", "calm": "Calm", "enthusiastic": "Enthusiastic", "excited": "Excited", "frustrated": "Frustrated", "sad": "Sad", "serious": "Serious", "smiling": "Smiling"}, "skinColor": {"label": "<PERSON>", "placeholder": "Select skin tone"}, "category": {"label": "Digital Human Category", "placeholder": "Select digital human category", "virtualHost": "Virtual Host", "teachingAssistant": "Teaching Assistant", "entertainmentHost": "Entertainment Host", "businessRepresentative": "Business Representative", "medicalAssistant": "Medical Assistant"}, "status": {"label": "Status", "placeholder": "Select status", "enabled": "Enabled", "disabled": "Disabled"}, "sort": {"placeholder": "Select sort method", "popular": "Most Popular", "newest": "Newest", "aToZ": "A-Z", "zToA": "Z-A"}, "table": {"name": "Name", "cover": "Cover", "category": "Category", "memberType": "Member Type", "usageCount": "Usage Count", "favoriteCount": "Favorite Count", "createDate": "Create Date", "actions": "Actions"}, "actions": {"edit": "Edit", "copy": "Copy ID", "delete": "Delete"}}, "renderLog": {"title": "Render Log", "description": "This is React.js Render Log page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "Render Log", "conditionQuery": "Condition Query", "conditionField": {"select": "Please Select", "userId": "User ID", "userName": "User Name", "userEmail": "User Email"}, "conditionValue": {"placeholder": "Please enter keyword"}, "generateDate": "Generate Date", "source": "Source", "status": "Status", "query": "Query", "collapse": "Collapse", "reset": "Reset", "tabs": {"digitalAvatar": "Digital Avatar"}, "fileId": "File ID", "id": "ID", "userId": "User ID", "fileContent": "File Content", "generateTime": "Generate Time", "actions": "Actions", "viewDetails": "View Details", "unavailable": "Unavailable", "copy": "Copy", "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed", "sources": {"all": "All", "preview": "Preview", "export": "Export"}, "statuses": {"all": "All", "success": "Success", "failed": "Failed", "generating": "Generating"}}, "inviteCodeManagement": {"title": "Invite Code Management", "description": "This is React.js Invite Code Management page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template", "breadcrumb": "Invite Code Management", "conditionQuery": "Condition Query", "conditionField": {"select": "Please Select", "id": "ID", "userName": "Name", "userEmail": "Email", "inviteCode": "Invite Code"}, "conditionValue": {"placeholder": "Please enter search content"}, "registerTime": "Register Time", "region": "Region", "createInviteCode": "Create Invite Code", "query": "Query", "collapse": "Collapse", "inviteCode": "Invite Code", "inviter": "Inviter", "userName": "User Name", "userEmail": "User Email", "usedInviteCodes": "Used Invite Codes", "reward": "<PERSON><PERSON>", "actions": "Actions", "details": "Details", "status": {"active": "Active", "used": "Used Up", "expired": "Expired", "unknown": "Unknown"}, "regions": {"all": "All Regions", "china": "China", "singapore": "Singapore", "usa": "USA", "uk": "UK", "canada": "Canada", "hongkong": "Hong Kong"}, "messages": {"queryComplete": "Query completed!", "formReset": "Form has been reset", "createInviteCodeInDevelopment": "Create invite code feature is in development", "viewingDetails": "Viewing invite code {code} details", "copied": "<PERSON>pied", "copyFailed": "<PERSON><PERSON> Failed"}}}, "common": {"language": "Language", "english": "English", "chinese": "中文", "loading": "Loading...", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "view": "View", "add": "Add", "close": "Close", "submit": "Submit", "reset": "Reset", "start": "Start", "end": "End", "goBack": "Go Back"}, "pagination": {"total": "Total {{total}} items", "page": "Page", "goTo": "Go to", "itemsPerPage": "/page"}}